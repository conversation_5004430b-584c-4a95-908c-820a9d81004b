import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { withAuth } from '@/lib/api/withAuth';

export interface VariableWithContext {
  name: string;
  source_level: 'Template' | 'Category' | 'Dataset';
  is_active: boolean;
  is_overridden: boolean;
  value: any;
  gui_config?: any;
  links?: any[];
}

export interface VariableTreeNode {
  id: number;
  name: string;
  description?: string;
  type: 'category' | 'dataset';
  level?: number;
  variables: VariableWithContext[];
  datasets?: VariableTreeNode[];
  children?: VariableTreeNode[];
}

export interface VariableTreeResponse {
  template_id: number;
  template_variables: VariableWithContext[];
  tree: VariableTreeNode[];
}

export const GET = withAuth(async (userId, request) => {
  try {
    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('templateId');

    if (!templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    const supabase = createClient();

    // Call the RPC function
    const { data, error } = await supabase.rpc('get_variable_tree_with_context', {
      p_template_id: parseInt(templateId),
      p_user_id: userId
    });

    if (error) {
      console.error('Error calling get_variable_tree_with_context:', error);
      return NextResponse.json(
        { error: 'Failed to fetch variable tree' },
        { status: 500 }
      );
    }

    if (!data) {
      return NextResponse.json(
        { error: 'Template not found or access denied' },
        { status: 404 }
      );
    }

    // Return the structured response
    const response: VariableTreeResponse = {
      template_id: data.template_id,
      template_variables: data.template_variables || [],
      tree: data.tree || []
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Variable tree API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

// Optional: Add POST method for future variable updates
export async function POST(request: NextRequest) {
  return NextResponse.json(
    { error: 'Method not implemented yet' },
    { status: 501 }
  );
}