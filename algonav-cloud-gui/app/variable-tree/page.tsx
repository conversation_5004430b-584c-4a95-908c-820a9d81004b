"use client"

import { useState, useEffect } from 'react';
import { Box, Typography, Paper, CircularProgress, Alert, FormControl, InputLabel, Select, MenuItem } from '@mui/material';
import { PageContainer } from '@/components/layout/PageContainer';
import { ErrorDialog } from '@/components/dialogs/ErrorDialog';
import { VariableTreeView } from '@/components/template/VariableTreeView';
import { useVariableTree } from '@/lib/hooks/useVariableTree';
import { useTemplates } from '@/lib/hooks/useTemplates';

export default function VariableTreePage() {
    const [selectedTemplateId, setSelectedTemplateId] = useState<string>('');
    const [error, setError] = useState<string | null>(null);
    
    const { data: templatesData, isLoading: templatesLoading, error: templatesError } = useTemplates();
    const { data: variableTreeData, loading: treeLoading, error: treeError } = useVariableTree({
        templateId: parseInt(selectedTemplateId),
        enabled: !!selectedTemplateId
    });

    useEffect(() => {
        if (templatesError) {
            setError(templatesError.message || 'Failed to load templates');
        }
    }, [templatesError]);

    useEffect(() => {
        if (treeError) {
            setError(treeError || 'Failed to load variable tree');
        }
    }, [treeError]);

    // Auto-select first template if available
    useEffect(() => {
        if (templatesData?.data && templatesData.data.length > 0 && !selectedTemplateId) {
            setSelectedTemplateId(templatesData.data[0].id.toString());
        }
    }, [templatesData, selectedTemplateId]);

    const handleTemplateChange = (templateId: string) => {
        setSelectedTemplateId(templateId);
        setError(null);
    };

    if (templatesLoading) {
        return (
            <PageContainer>
                <Box display="flex" justifyContent="center" p={4}>
                    <CircularProgress />
                </Box>
            </PageContainer>
        );
    }

    if (!templatesData?.data || templatesData.data.length === 0) {
        return (
            <PageContainer>
                <Alert severity="info">No templates found. Create a template first to view its variable tree.</Alert>
            </PageContainer>
        );
    }

    return (
        <PageContainer>
            <Paper elevation={0} sx={{ p: 3, mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h4" component="h1" gutterBottom>
                        Variable Tree
                    </Typography>
                    <FormControl sx={{ minWidth: 300 }}>
                        <InputLabel id="template-select-label">Select Template</InputLabel>
                        <Select
                            labelId="template-select-label"
                            value={selectedTemplateId}
                            label="Select Template"
                            onChange={(e) => handleTemplateChange(e.target.value)}
                        >
                            {templatesData.data.map((template: any) => (
                                <MenuItem key={template.id} value={template.id.toString()}>
                                    {template.name}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                </Box>
                <Typography variant="body1" color="text.secondary" gutterBottom>
                    View the hierarchical structure of variables for the selected template, including template-level variables and category/dataset overrides.
                </Typography>
            </Paper>

            {selectedTemplateId && (
                <Paper elevation={0} sx={{ p: 3 }}>
                    {treeLoading ? (
                        <Box display="flex" justifyContent="center" p={4}>
                            <CircularProgress />
                        </Box>
                    ) : variableTreeData ? (
                        <VariableTreeView templateId={parseInt(selectedTemplateId)} />
                    ) : (
                        <Alert severity="info">No variable tree data available for this template.</Alert>
                    )}
                </Paper>
            )}

            <ErrorDialog
                open={!!error}
                onClose={() => setError(null)}
                error={error}
                title="Variable Tree Error"
            />
        </PageContainer>
    );
}